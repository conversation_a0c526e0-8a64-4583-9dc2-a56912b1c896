#include <cassert>

#include <string>
#include "property.h"

int main(int argc, char* argv[]) {
    Property<std::string> firstname("<PERSON>");
    Property<std::string> lastname("<PERSON>");
    Property<int> age(argc);
    Property<std::string> fullname;
    fullname.setBinding([&]() {
        return firstname.value() + " "
               + lastname.value() + " age: "
               + std::to_string(age.value());
    });
    std::cout << fullname.value() << std::endl;
    assert(fullname.value() == "<PERSON> age: " + std::to_string(arg));

    firstname = "Emma";
    std::cout << fullname.value() << std::endl;
    assert(fullname.value() == "<PERSON> Smith age: " + std::to_string(arg));

    age.setValue(age.value() + 1);

    std::cout << fullname.value() << std::endl;
    assert(fullname.value() == "<PERSON> Smith age: " + std::to_string(arg + 1));
}
